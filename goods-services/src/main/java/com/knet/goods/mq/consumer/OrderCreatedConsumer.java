package com.knet.goods.mq.consumer;

import com.knet.common.annotation.DistributedLock;
import com.knet.common.utils.RedisCacheUtil;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2025/6/6 15:14
 * @description: 订单创建消费者
 */
@Slf4j
@Component
public class OrderCreatedConsumer {
    @Resource
    private RedisCacheUtil redisCacheUtil;

    @DistributedLock(key = "'orderConsumer", expire = 2)
    @RabbitListener(
            queues = "order-queue.${spring.application.name}",
            ackMode = "MANUAL" // 必须显式指定
    )
    public void handleOrderNotification(
            @Payload String messageBody,
            @Header("routingKey") String orderType,
            @Header("messageId") String messageId,
            Channel channel,
            @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) {
        try {
            if ("order.create".equals(orderType) || "order.created".equals(orderType)) {
                // 幂等性检查
                if (!redisCacheUtil.setIfAbsent(messageId, "PROCESSED", 60)) {
                    log.warn("重复消息: {}", messageId);
                    channel.basicAck(deliveryTag, false);
                    return;
                }
                // 业务处理
                processOrder(messageBody);
                channel.basicAck(deliveryTag, false);
            }
        } catch (Exception e) {
            log.error("订单处理失败: {}", messageBody, e);
            try {
                // 拒绝消息并重新入队
                channel.basicNack(deliveryTag, false, true);
            } catch (IOException ex) {
                log.error("消息拒绝失败: {}", ex.getMessage());
            }
        }
    }

    /**
     * todo 库存检查锁定
     *
     * @param messageBody 消息体
     */
    private void processOrder(String messageBody) {
        // 业务逻辑
        log.info("业务逻辑: {}", messageBody);
    }
}
